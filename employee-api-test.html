<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>员工信息接口API测试工具</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="lib/layui-v2.9.21/css/layui.css">
    <style>
        body { padding: 20px; background-color: #f5f5f5; }
        .test-container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .title { text-align: center; color: #333; margin-bottom: 30px; }
        .result-box { background: #f8f8f8; border: 1px solid #ddd; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; font-size: 12px; }
        .success { background: #f0f9ff; border-color: #1E9FFF; }
        .error { background: #fff2f0; border-color: #ff5722; }
        .quick-test { margin: 15px 0; }
        .quick-test button { margin-right: 10px; margin-bottom: 10px; }
        .api-info { background: #f0f9ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #1E9FFF; }
        .param-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        @media (max-width: 768px) { .param-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">员工信息接口API测试工具</h1>

        <!-- API信息说明 -->
        <div class="api-info">
            <h3>接口信息</h3>
            <p><strong>请求地址：</strong>https://***********:6014/api/AtndStatsExtApi/QueryArchiveList</p>
            <p><strong>请求方式：</strong>POST</p>
            <p><strong>Content-Type：</strong>application/x-www-form-urlencoded;charset=utf-8</p>
            <p><strong>认证Token：</strong>RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB</p>
        </div>
        
        <!-- 快速测试按钮 -->
        <div class="layui-card">
            <div class="layui-card-header">快速测试</div>
            <div class="layui-card-body quick-test">
                <button class="layui-btn" onclick="testAllEmployees()">查询所有员工</button>
                <button class="layui-btn layui-btn-normal" onclick="testActiveEmployees()">查询在职员工</button>
                <button class="layui-btn layui-btn-warm" onclick="testByDepartment()">按部门查询</button>
                <button class="layui-btn layui-btn-danger" onclick="clearResults()">清空结果</button>
            </div>
        </div>

        <!-- 员工查询条件 -->
        <div class="layui-card">
            <div class="layui-card-header">员工查询条件</div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="employeeForm">
                    <div class="param-grid">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工号:</label>
                            <div class="layui-input-block">
                                <input type="text" name="jobNo" placeholder="员工工号" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">姓名:</label>
                            <div class="layui-input-block">
                                <input type="text" name="memberCn" placeholder="员工姓名" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">部门名称:</label>
                            <div class="layui-input-block">
                                <input type="text" name="orgCn" placeholder="部门名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">员工类型:</label>
                            <div class="layui-input-block">
                                <select name="jobType">
                                    <option value="">请选择</option>
                                    <option value="0">STAFF</option>
                                    <option value="1">DAILY</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">员工状态:</label>
                            <div class="layui-input-block">
                                <select name="archiveStatus">
                                    <option value="">请选择</option>
                                    <option value="1">在职</option>
                                    <option value="0">离职</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">入厂日期开始:</label>
                            <div class="layui-input-block">
                                <input type="text" name="entryDateStart" placeholder="yyyy-MM-dd" class="layui-input" id="entryStart">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">入厂日期结束:</label>
                            <div class="layui-input-block">
                                <input type="text" name="entryDateEnd" placeholder="yyyy-MM-dd" class="layui-input" id="entryEnd">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">离职日期开始:</label>
                            <div class="layui-input-block">
                                <input type="text" name="departureStart" placeholder="yyyy-MM-dd" class="layui-input" id="departureStart">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">离职日期结束:</label>
                            <div class="layui-input-block">
                                <input type="text" name="departureEnd" placeholder="yyyy-MM-dd" class="layui-input" id="departureEnd">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">Token:</label>
                            <div class="layui-input-block">
                                <input type="text" name="token" value="RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB" class="layui-input" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="text-align: center; margin-top: 20px;">
                        <button type="button" class="layui-btn" onclick="sendEmployeeQuery()">查询员工信息</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="resetForm()">重置条件</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 结果显示 -->
        <div class="layui-card">
            <div class="layui-card-header">测试结果</div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">请求信息:</label>
                    <div class="layui-input-block">
                        <div id="requestInfo" class="result-box">等待发送请求...</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">响应结果:</label>
                    <div class="layui-input-block">
                        <div id="responseResult" class="result-box">等待响应...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="lib/layui-v2.9.21/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'laydate'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            var $ = layui.jquery;

            // 初始化日期选择器
            laydate.render({
                elem: '#entryStart',
                type: 'date'
            });
            laydate.render({
                elem: '#entryEnd',
                type: 'date'
            });
            laydate.render({
                elem: '#departureStart',
                type: 'date'
            });
            laydate.render({
                elem: '#departureEnd',
                type: 'date'
            });

            // 查询所有员工
            window.testAllEmployees = function() {
                var requestData = {
                    url: 'https://***********:6014/api/AtndStatsExtApi/QueryArchiveList',
                    method: 'POST',
                    data: {
                        token: 'RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB'
                    }
                };
                sendRequest(requestData, '查询所有员工');
            };

            // 查询在职员工
            window.testActiveEmployees = function() {
                var requestData = {
                    url: 'https://***********:6014/api/AtndStatsExtApi/QueryArchiveList',
                    method: 'POST',
                    data: {
                        dto: JSON.stringify({
                            archiveStatus: 1
                        }),
                        token: 'RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB'
                    }
                };
                sendRequest(requestData, '查询在职员工');
            };

            // 按部门查询
            window.testByDepartment = function() {
                layer.prompt({
                    title: '请输入部门名称',
                    formType: 0
                }, function(value, index){
                    layer.close(index);
                    var requestData = {
                        url: 'https://***********:6014/api/AtndStatsExtApi/QueryArchiveList',
                        method: 'POST',
                        data: {
                            dto: JSON.stringify({
                                orgCn: value
                            }),
                            token: 'RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB'
                        }
                    };
                    sendRequest(requestData, '按部门查询员工 - ' + value);
                });
            };

            // 发送员工查询请求
            window.sendEmployeeQuery = function() {
                var formData = form.val('employeeForm');

                // 构建dto参数
                var dto = {};
                if(formData.jobNo) dto.jobNo = formData.jobNo;
                if(formData.memberCn) dto.memberCn = formData.memberCn;
                if(formData.orgCn) dto.orgCn = formData.orgCn;
                if(formData.jobType !== '') dto.jobType = parseInt(formData.jobType);
                if(formData.archiveStatus !== '') dto.archiveStatus = parseInt(formData.archiveStatus);
                if(formData.entryDateStart) dto.entryDateStart = formData.entryDateStart;
                if(formData.entryDateEnd) dto.entryDateEnd = formData.entryDateEnd;
                if(formData.departureStart) dto.departureStart = formData.departureStart;
                if(formData.departureEnd) dto.departureEnd = formData.departureEnd;

                var requestData = {
                    url: 'https://***********:6014/api/AtndStatsExtApi/QueryArchiveList',
                    method: 'POST',
                    data: {
                        token: formData.token
                    }
                };

                // 如果有查询条件，添加dto参数
                if(Object.keys(dto).length > 0) {
                    requestData.data.dto = JSON.stringify(dto);
                }

                sendRequest(requestData, '自定义员工查询');
            };

            // 重置表单
            window.resetForm = function() {
                document.querySelector('form[lay-filter="employeeForm"]').reset();
                form.render();
            };

            // 清空结果
            window.clearResults = function() {
                $('#requestInfo').removeClass('success error').text('已清空');
                $('#responseResult').removeClass('success error').text('已清空');
            };

            // 发送请求的通用方法
            function sendRequest(requestData, testName) {
                var startTime = new Date();
                
                // 显示请求信息
                var requestInfo = testName + '\n';
                requestInfo += '时间: ' + startTime.toLocaleString() + '\n';
                requestInfo += '方法: ' + requestData.method + '\n';
                requestInfo += 'URL: ' + requestData.url + '\n';
                requestInfo += '参数: ' + JSON.stringify(requestData.data, null, 2);
                $('#requestInfo').removeClass('success error').text(requestInfo);

                // 构建AJAX配置
                var ajaxConfig = {
                    url: requestData.url,
                    type: requestData.method,
                    data: requestData.data,
                    timeout: 30000,
                    crossDomain: true,
                    beforeSend: function(xhr) {
                        // 设置Content-Type为表单格式
                        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded;charset=utf-8');
                        // 添加CORS头
                        xhr.setRequestHeader('Access-Control-Allow-Origin', '*');
                        xhr.setRequestHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
                        xhr.setRequestHeader('Access-Control-Allow-Headers', 'Content-Type');
                    },
                    success: function(response, textStatus, xhr) {
                        var endTime = new Date();
                        var duration = endTime - startTime;
                        
                        var result = '✅ 请求成功\n';
                        result += '状态码: ' + xhr.status + ' ' + xhr.statusText + '\n';
                        result += '耗时: ' + duration + 'ms\n';
                        result += '响应时间: ' + endTime.toLocaleString() + '\n\n';
                        result += '响应内容:\n';
                        
                        if(typeof response === 'string') {
                            try {
                                var jsonResponse = JSON.parse(response);
                                result += JSON.stringify(jsonResponse, null, 2);
                            } catch(e) {
                                result += response;
                            }
                        } else {
                            result += JSON.stringify(response, null, 2);
                        }
                        
                        $('#responseResult').removeClass('error').addClass('success').text(result);
                        layer.msg('请求成功', {icon: 1});
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        var endTime = new Date();
                        var duration = endTime - startTime;
                        
                        var result = '❌ 请求失败\n';
                        result += '状态码: ' + xhr.status + ' ' + xhr.statusText + '\n';
                        result += '错误类型: ' + textStatus + '\n';
                        result += '错误信息: ' + (errorThrown || '未知错误') + '\n';
                        result += '耗时: ' + duration + 'ms\n';
                        result += '响应时间: ' + endTime.toLocaleString() + '\n\n';
                        
                        if(xhr.responseText) {
                            result += '响应内容:\n';
                            try {
                                var jsonResponse = JSON.parse(xhr.responseText);
                                result += JSON.stringify(jsonResponse, null, 2);
                            } catch(e) {
                                result += xhr.responseText;
                            }
                        }
                        
                        $('#responseResult').removeClass('success').addClass('error').text(result);
                        layer.msg('请求失败: ' + textStatus, {icon: 2});
                    }
                };

                // 显示加载状态
                $('#responseResult').removeClass('success error').text('正在发送请求...');
                
                // 发送请求
                $.ajax(ajaxConfig);
            }
        });
    </script>
</body>
</html>
