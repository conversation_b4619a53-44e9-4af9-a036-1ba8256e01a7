员工信息接口说明

请求说明
请求地址	http://172.20.0.17:6014/api/AtndStatsExtApi/QueryArchiveList
请求方式	POST 
(ps. 接口的Content-Type=application/x-www-form-urlencoded;charset:utf-8;)
请求参数	参数名	参数说明
	dto	该参数为选填, 用于筛选员工信息;
条件参数说明:
参数名	参数类型	参数描述
jobNo	string	工号
jobType	int	员工类型 0=STAFF,  1=DAILY
memberCn	string	姓名
orgCn	string	部门名称
departureStart	DateTime	离职日期范围开始
departureEnd	DateTime	离职日期范围结束
entryDateStart	DateTime	入厂日期范围开始
entryDateEnd	DateTime	入厂日期范围结束
archiveStatus	int	员工状态 1=在职,  0=离职
		


	token	该参数为必填，用于接口调用者校验身份；
参数值= RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB
