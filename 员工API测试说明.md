# 员工信息API测试工具使用说明

## 概述
这个测试工具用于验证员工信息接口API的可用性，解决在Postman中遇到的身份验证问题。

## 文件说明
- `employee-api-test.html` - 主要的测试页面
- `ashx/ProxyApi.ashx` - 代理处理程序，用于解决跨域和SSL问题
- `ashx/ProxyApi.ashx.cs` - 代理处理程序的后端代码

## API信息
- **请求地址**: http://***********:6014/api/AtndStatsExtApi/QueryArchiveList
- **请求方式**: POST
- **Content-Type**: application/x-www-form-urlencoded;charset=utf-8
- **认证Token**: RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB

## 使用方法

### 1. 访问测试页面
在浏览器中打开：`http://localhost:端口号/employee-api-test.html`

### 2. 代理模式（推荐）
- 默认开启代理模式，可以避免跨域和SSL问题
- 代理会自动处理请求转发和响应格式化

### 3. 快速测试
页面提供了三个快速测试按钮：
- **查询所有员工**: 不带任何筛选条件
- **查询在职员工**: 筛选状态为在职的员工
- **按部门查询**: 输入部门名称进行查询

### 4. 自定义查询
可以设置以下查询条件：
- 工号 (jobNo)
- 姓名 (memberCn)
- 部门名称 (orgCn)
- 员工类型 (jobType): 0=STAFF, 1=DAILY
- 员工状态 (archiveStatus): 1=在职, 0=离职
- 入厂日期范围 (entryDateStart/entryDateEnd)
- 离职日期范围 (departureStart/departureEnd)

## 问题解决

### SSL协议错误 (ERR_SSL_PROTOCOL_ERROR)
这个错误通常是因为：
1. 目标API服务器的SSL配置问题
2. 浏览器的安全策略限制
3. 网络环境的限制

**解决方案**：
1. 使用代理模式（默认开启）
2. 检查网络连接
3. 确认API服务器地址是否正确

### 身份验证失败
如果遇到身份验证问题：
1. 确认Token是否正确：`RTF2VY62EO9X1AW21DSS5NVN7GIO9MYB`
2. 检查请求格式是否为表单格式
3. 使用代理模式避免浏览器安全限制

### 跨域问题
代理模式会自动处理跨域问题，如果仍有问题：
1. 确认代理服务正常运行
2. 检查服务器CORS配置

## 响应格式
成功的响应会显示：
- 请求状态和耗时
- API返回的员工数据
- 详细的错误信息（如果有）

## 注意事项
1. 确保网络可以访问目标API地址
2. 如果修改了API地址或Token，需要更新代码
3. 代理模式需要本地服务器支持.NET Framework
4. 测试时注意数据安全，不要在生产环境进行大量测试

## 技术实现
- 前端：Layui框架 + jQuery
- 后端代理：ASP.NET Handler
- 跨域处理：自定义代理转发
- SSL处理：忽略证书验证（仅用于测试）
