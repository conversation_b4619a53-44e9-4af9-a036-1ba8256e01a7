using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using System.Web.SessionState;
using Newtonsoft.Json;

namespace WebApplication1
{
    /// <summary>
    /// API代理处理程序，用于解决跨域和SSL问题
    /// </summary>
    public class ProxyApi : <PERSON><PERSON>ttp<PERSON><PERSON><PERSON>, IRequiresSessionState
    {
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            context.Response.AddHeader("Access-Control-Allow-Origin", "*");
            context.Response.AddHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            context.Response.AddHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

            try
            {
                // 处理OPTIONS预检请求
                if (context.Request.HttpMethod == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    return;
                }

                // 获取目标API地址
                string targetUrl = context.Request.Params["targetUrl"];
                if (string.IsNullOrEmpty(targetUrl))
                {
                    targetUrl = "http://172.20.0.17:6014/api/AtndStatsExtApi/QueryArchiveList";
                }

                // 获取请求方法
                string method = context.Request.Params["method"] ?? "POST";

                // 创建HTTP请求
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(targetUrl);
                request.Method = method;
                request.ContentType = "application/x-www-form-urlencoded;charset=utf-8";
                request.Timeout = 30000;

                // 忽略SSL证书错误
                ServicePointManager.ServerCertificateValidationCallback = 
                    (sender, certificate, chain, sslPolicyErrors) => true;

                // 如果是POST请求，添加请求体
                if (method.ToUpper() == "POST")
                {
                    // 构建请求参数
                    StringBuilder postData = new StringBuilder();
                    
                    // 添加token参数
                    string token = context.Request.Params["token"];
                    if (!string.IsNullOrEmpty(token))
                    {
                        postData.Append("token=" + HttpUtility.UrlEncode(token));
                    }

                    // 添加dto参数
                    string dto = context.Request.Params["dto"];
                    if (!string.IsNullOrEmpty(dto))
                    {
                        if (postData.Length > 0) postData.Append("&");
                        postData.Append("dto=" + HttpUtility.UrlEncode(dto));
                    }

                    // 添加其他参数
                    foreach (string key in context.Request.Form.AllKeys)
                    {
                        if (key != "targetUrl" && key != "method" && key != "token" && key != "dto")
                        {
                            if (postData.Length > 0) postData.Append("&");
                            postData.Append(key + "=" + HttpUtility.UrlEncode(context.Request.Form[key]));
                        }
                    }

                    byte[] data = Encoding.UTF8.GetBytes(postData.ToString());
                    request.ContentLength = data.Length;

                    using (Stream requestStream = request.GetRequestStream())
                    {
                        requestStream.Write(data, 0, data.Length);
                    }
                }

                // 发送请求并获取响应
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    context.Response.StatusCode = (int)response.StatusCode;
                    
                    using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                    {
                        string responseText = reader.ReadToEnd();
                        
                        // 构建响应对象
                        var result = new
                        {
                            success = true,
                            statusCode = (int)response.StatusCode,
                            statusText = response.StatusDescription,
                            data = responseText,
                            headers = GetResponseHeaders(response),
                            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        };

                        context.Response.Write(JsonConvert.SerializeObject(result));
                    }
                }
            }
            catch (WebException ex)
            {
                // 处理HTTP错误
                var errorResponse = new
                {
                    success = false,
                    error = "WebException",
                    message = ex.Message,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                if (ex.Response != null)
                {
                    using (var errorStream = ex.Response.GetResponseStream())
                    using (var reader = new StreamReader(errorStream))
                    {
                        errorResponse = new
                        {
                            success = false,
                            error = "WebException",
                            message = ex.Message,
                            statusCode = (int)((HttpWebResponse)ex.Response).StatusCode,
                            responseText = reader.ReadToEnd(),
                            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        };
                    }
                }

                context.Response.StatusCode = 500;
                context.Response.Write(JsonConvert.SerializeObject(errorResponse));
            }
            catch (Exception ex)
            {
                // 处理其他错误
                var errorResponse = new
                {
                    success = false,
                    error = ex.GetType().Name,
                    message = ex.Message,
                    stackTrace = ex.StackTrace,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                context.Response.StatusCode = 500;
                context.Response.Write(JsonConvert.SerializeObject(errorResponse));
            }
        }

        private Dictionary<string, string> GetResponseHeaders(HttpWebResponse response)
        {
            var headers = new Dictionary<string, string>();
            foreach (string headerName in response.Headers.AllKeys)
            {
                headers[headerName] = response.Headers[headerName];
            }
            return headers;
        }

        public bool IsReusable
        {
            get { return false; }
        }
    }
}
